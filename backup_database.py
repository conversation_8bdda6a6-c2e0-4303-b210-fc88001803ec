#!/usr/bin/env python3
"""
Script to backup database before clearing data.
Creates a SQL dump file with all data.
"""

import os
import sys
import subprocess
from datetime import datetime
from pathlib import Path

def create_backup():
    """
    Create a MySQL backup using mysqldump.
    """
    # Get database configuration from environment
    db_host = os.getenv("MYSQL_HOST", "**************")
    db_port = os.getenv("MYSQL_PORT", "33061")
    db_user = os.getenv("MYSQL_USER", "root")
    db_password = os.getenv("MYSQL_PASSWORD", "root@H123")
    db_name = os.getenv("MYSQL_DATABASE", "atn_tolery")
    
    # Create backup filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"backup_{db_name}_{timestamp}.sql"
    backup_path = Path("backups") / backup_filename
    
    # Create backups directory if it doesn't exist
    backup_path.parent.mkdir(exist_ok=True)
    
    print(f"📊 Database: {db_name}")
    print(f"🏠 Host: {db_host}:{db_port}")
    print(f"👤 User: {db_user}")
    print(f"💾 Backup file: {backup_path}")
    
    # Construct mysqldump command
    cmd = [
        "mysqldump",
        f"--host={db_host}",
        f"--port={db_port}",
        f"--user={db_user}",
        f"--password={db_password}",
        "--single-transaction",
        "--routines",
        "--triggers",
        "--add-drop-table",
        db_name
    ]
    
    try:
        print("\n🔄 Creating backup...")
        
        # Run mysqldump and save to file
        with open(backup_path, 'w') as backup_file:
            result = subprocess.run(
                cmd,
                stdout=backup_file,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )
        
        # Check if backup file was created and has content
        if backup_path.exists() and backup_path.stat().st_size > 0:
            file_size = backup_path.stat().st_size
            print(f"✅ Backup created successfully!")
            print(f"📁 File: {backup_path}")
            print(f"📏 Size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
            return str(backup_path)
        else:
            print("❌ Backup file was not created or is empty")
            return None
            
    except subprocess.CalledProcessError as e:
        print(f"❌ mysqldump failed: {e}")
        if e.stderr:
            print(f"Error details: {e.stderr}")
        return None
    except FileNotFoundError:
        print("❌ mysqldump command not found. Please install MySQL client tools.")
        print("On Ubuntu/Debian: sudo apt-get install mysql-client")
        print("On CentOS/RHEL: sudo yum install mysql")
        return None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None

def main():
    """
    Main function to create database backup.
    """
    print("💾 Tolery Database Backup Tool")
    print("=" * 40)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Create backup
    backup_file = create_backup()
    
    if backup_file:
        print(f"\n🎉 Backup completed successfully!")
        print(f"📁 Backup saved to: {backup_file}")
        print("\n💡 You can now safely clear the database using:")
        print("   python clear_database.py")
        print("\n🔄 To restore from backup later, use:")
        print(f"   mysql -h {os.getenv('MYSQL_HOST')} -P {os.getenv('MYSQL_PORT')} -u {os.getenv('MYSQL_USER')} -p {os.getenv('MYSQL_DATABASE')} < {backup_file}")
    else:
        print("\n❌ Backup failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
