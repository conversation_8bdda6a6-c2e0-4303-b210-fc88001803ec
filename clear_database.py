#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to clear all data from the database while keeping the database structure.
This will delete all records from all tables but preserve the table schemas.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.database.database import get_db, engine
    from src.models.sessions import Session as SessionModel, ChatHistory
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy import text
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

def clear_all_data():
    """
    Clear all data from the database while preserving table structure.
    """
    print("🗑️  Starting database cleanup...")
    
    # Create a new session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Get database info
        db_name = os.getenv("MYSQL_DATABASE", "atn_tolery")
        print(f"📊 Database: {db_name}")
        
        # Disable foreign key checks temporarily
        print("🔓 Disabling foreign key checks...")
        db.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
        
        # Get all table names
        result = db.execute(text("SHOW TABLES"))
        tables = [row[0] for row in result.fetchall()]
        
        print(f"📋 Found {len(tables)} tables:")
        for table in tables:
            print(f"   - {table}")
        
        # Clear each table
        print("\n🧹 Clearing table data...")
        for table in tables:
            try:
                # Get row count before deletion
                count_result = db.execute(text(f"SELECT COUNT(*) FROM `{table}`"))
                row_count = count_result.fetchone()[0]
                
                if row_count > 0:
                    # Delete all rows from the table
                    db.execute(text(f"DELETE FROM `{table}`"))
                    print(f"   ✅ Cleared {row_count} rows from `{table}`")
                else:
                    print(f"   ⚪ Table `{table}` was already empty")
                    
            except Exception as e:
                print(f"   ❌ Error clearing table `{table}`: {e}")
        
        # Reset auto-increment counters
        print("\n🔄 Resetting auto-increment counters...")
        for table in tables:
            try:
                db.execute(text(f"ALTER TABLE `{table}` AUTO_INCREMENT = 1"))
                print(f"   ✅ Reset auto-increment for `{table}`")
            except Exception as e:
                print(f"   ⚠️  Could not reset auto-increment for `{table}`: {e}")
        
        # Re-enable foreign key checks
        print("\n🔒 Re-enabling foreign key checks...")
        db.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
        
        # Commit all changes
        db.commit()
        print("\n✅ Database cleanup completed successfully!")
        
        # Verify tables are empty
        print("\n📊 Verification - Table row counts:")
        for table in tables:
            try:
                count_result = db.execute(text(f"SELECT COUNT(*) FROM `{table}`"))
                row_count = count_result.fetchone()[0]
                print(f"   - {table}: {row_count} rows")
            except Exception as e:
                print(f"   - {table}: Error checking count - {e}")
                
    except Exception as e:
        print(f"❌ Error during database cleanup: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def confirm_action():
    """
    Ask user for confirmation before proceeding.
    """
    print("⚠️  WARNING: This will delete ALL data from the database!")
    print("📋 This includes:")
    print("   - All chat sessions")
    print("   - All chat history")
    print("   - All generated files references")
    print("   - All user data")
    print("\n🔧 The database structure (tables, columns) will be preserved.")
    
    response = input("\n❓ Are you sure you want to proceed? (type 'YES' to confirm): ")
    
    if response.strip().upper() == 'YES':
        return True
    else:
        print("❌ Operation cancelled.")
        return False

def main():
    """
    Main function to execute database cleanup.
    """
    print("🗄️  Tolery Database Cleanup Tool")
    print("=" * 50)
    
    # Check database connection
    try:
        db = next(get_db())
        db.execute(text("SELECT 1"))
        db.close()
        print("✅ Database connection successful")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("Please check your database configuration in .env file")
        sys.exit(1)
    
    # Get confirmation
    if not confirm_action():
        sys.exit(0)
    
    # Perform cleanup
    try:
        clear_all_data()
        print("\n🎉 Database cleanup completed successfully!")
        print("💡 You can now start fresh with a clean database.")
        
    except Exception as e:
        print(f"\n❌ Database cleanup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
