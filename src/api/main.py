"""
Main API Application - Clean Version
Only contains API-PRODUCTION and API-TEST mounting.
All other endpoints have been moved to their respective API modules.
"""

import os
import sys
import logging
import platform
from datetime import datetime
from pathlib import Path
from typing import Optional

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("tolery-main")

# ============================================================================
# BASE_URL CONFIGURATION
# ============================================================================

# Get domain and port from environment
DOMAIN = os.getenv("DOMAIN", "http://localhost")
PORT = int(os.getenv("UVICORN_PORT", 8124))

# Only include port in BASE_URL if DOMAIN is localhost
if DOMAIN == "http://localhost" or DOMAIN == "localhost":
    BASE_URL = f"{DOMAIN}:{PORT}"
else:
    BASE_URL = DOMAIN

logger.info(f"Configured BASE_URL: {BASE_URL}")

# Add project root to Python path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import processors for health check
try:
    from .utils.pdf_handler import PDFProcessor
    from .utils.image_handler import ImageProcessor
    from .core.chatbot import text_to_cad_agent
except ImportError:
    from src.utils.pdf_handler import PDFProcessor
    from src.utils.image_handler import ImageProcessor
    from src.core.chatbot import text_to_cad_agent

# Initialize processors
pdf_processor = PDFProcessor(cad_agent=text_to_cad_agent)
image_processor = ImageProcessor(cad_agent=text_to_cad_agent)

# Create FastAPI app
app = FastAPI(
    title="Tolery CAD Generation API",
    description="Main application with API-PRODUCTION and API-TEST",
    version="2.0.0",
    docs_url="/docs",  # Main app documentation
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Static files and templates
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
    templates = Jinja2Templates(directory="templates")
except Exception as e:
    logger.warning(f"Could not mount static files or templates: {e}")
    templates = None

# ============================================================================
# BASIC ENDPOINTS FOR FRONTEND
# ============================================================================

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    if templates:
        return templates.TemplateResponse("index.html", {"request": request})
    else:
        return HTMLResponse("""
        <html>
            <head><title>Tolery CAD API</title></head>
            <body>
                <h1>Tolery CAD Generation API</h1>
                <p>API Documentation:</p>
                <ul>
                    <li><a href="/api-test/docs">API-TEST Documentation</a></li>
                    <li><a href="/api-production/docs">API-PRODUCTION Documentation</a></li>
                </ul>
            </body>
        </html>
        """)

# Health check endpoint
@app.get("/api/health", summary="Health check endpoint",
         description="Returns information about the API's health and environment")
async def health_check():
    """
    Health check endpoint that returns information about the API's health and environment.

    Returns:
        dict: A dictionary containing health information
    """
    return {
        "status": "ok",
        "timestamp": datetime.now().isoformat(),
        "version": "2.0.0",
        "environment": {
            "python_version": platform.python_version(),
            "system": platform.system(),
            "processor": platform.processor(),
            "pdf_processor_available": pdf_processor.client is not None
        },
        "apis": {
            "api_test": "/api-test/docs",
            "api_production": "/api-production/docs"
        }
    }

# ============================================================================
# ONLY 2 APIs: API-PRODUCTION and API-TEST
# ============================================================================

# Import and mount API-PRODUCTION
try:
    from .production.main import app as api_production_app
except ImportError:
    from src.api.production.main import app as api_production_app

app.mount("/api-production", api_production_app)

# Import and mount API-TEST
try:
    from .test.main import app as api_test_app
except ImportError:
    from src.api.test.main import app as api_test_app

app.mount("/api-test", api_test_app)

# ============================================================================
# DOWNLOAD ROUTER FOR FILE DOWNLOADS
# ============================================================================

# Import and mount download router for file downloads
try:
    from .routes.cad import download_router
except ImportError:
    from src.api.routes.cad import download_router

app.include_router(download_router)

# ============================================================================
# END OF FILE - All endpoints moved to API-PRODUCTION and API-TEST
# ============================================================================
