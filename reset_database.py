#!/usr/bin/env python3
"""
Complete database reset script:
1. Creates backup of current data
2. Clears all data from database
3. Keeps database structure intact
"""

import os
import sys
import subprocess
from pathlib import Path
from datetime import datetime

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def run_backup():
    """
    Run the backup script.
    """
    print("📋 Step 1: Creating database backup...")
    print("-" * 40)
    
    try:
        result = subprocess.run([
            sys.executable, "backup_database.py"
        ], check=True, capture_output=True, text=True)
        
        print(result.stdout)
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Backup failed: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False
    except Exception as e:
        print(f"❌ Unexpected error during backup: {e}")
        return False

def run_clear():
    """
    Run the clear database script.
    """
    print("\n📋 Step 2: Clearing database data...")
    print("-" * 40)
    
    try:
        # Import and run clear function directly
        from clear_database import clear_all_data
        clear_all_data()
        return True
        
    except Exception as e:
        print(f"❌ Database clear failed: {e}")
        return False

def main():
    """
    Main function to execute complete database reset.
    """
    print("🔄 Tolery Database Reset Tool")
    print("=" * 50)
    print("This tool will:")
    print("1. 💾 Create a backup of current database")
    print("2. 🗑️  Clear all data from database")
    print("3. 🏗️  Keep database structure intact")
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("⚠️  python-dotenv not found, using system environment variables")
    
    # Show database info
    db_name = os.getenv("MYSQL_DATABASE", "atn_tolery")
    db_host = os.getenv("MYSQL_HOST", "**************")
    db_port = os.getenv("MYSQL_PORT", "33061")
    
    print(f"\n📊 Target Database:")
    print(f"   - Name: {db_name}")
    print(f"   - Host: {db_host}:{db_port}")
    
    # Get confirmation
    print("\n⚠️  WARNING: This will delete ALL data from the database!")
    response = input("\n❓ Do you want to proceed? (type 'YES' to confirm): ")
    
    if response.strip().upper() != 'YES':
        print("❌ Operation cancelled.")
        sys.exit(0)
    
    # Step 1: Create backup
    print(f"\n🚀 Starting database reset process...")
    backup_success = run_backup()
    
    if not backup_success:
        print("\n❌ Backup failed! Aborting database clear for safety.")
        print("💡 Please fix backup issues before proceeding.")
        sys.exit(1)
    
    # Step 2: Clear database
    clear_success = run_clear()
    
    if clear_success:
        print("\n🎉 Database reset completed successfully!")
        print("\n📋 Summary:")
        print("   ✅ Backup created")
        print("   ✅ Database data cleared")
        print("   ✅ Database structure preserved")
        print("\n💡 Your database is now clean and ready for fresh data.")
        
        # Show backup location
        backups_dir = Path("backups")
        if backups_dir.exists():
            backup_files = list(backups_dir.glob("backup_*.sql"))
            if backup_files:
                latest_backup = max(backup_files, key=lambda x: x.stat().st_mtime)
                print(f"\n💾 Latest backup: {latest_backup}")
    else:
        print("\n❌ Database reset failed!")
        print("💡 Your data backup was created successfully, so no data was lost.")
        sys.exit(1)

if __name__ == "__main__":
    main()
