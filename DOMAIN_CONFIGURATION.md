# Domain Configuration Guide

## 🎯 **Cấu <PERSON>nh Domain cho Download URLs**

### **1. Thay <PERSON> Domain**

**Sửa file `.env`:**
```bash
# Thay đổi từ localhost sang domain thực
DOMAIN=https://your-production-domain.com

# Hoặc với subdomain
DOMAIN=https://api.yourdomain.com

# Hoặc với IP
DOMAIN=http://**************
```

### **2. Các Trường Hợp Sử Dụng**

**Development (localhost):**
```bash
DOMAIN=http://localhost
UVICORN_PORT=8124
# Kết quả: http://localhost:8124/download/...
```

**Production (domain):**
```bash
DOMAIN=https://api.yourdomain.com
UVICORN_PORT=8124
# Kết quả: https://api.yourdomain.com/download/...
```

**Production (IP):**
```bash
DOMAIN=http://**************
UVICORN_PORT=8124
# Kết quả: http://**************:8124/download/...
```

### **3. Logic Tự Động**

**Code sẽ tự động:**
- ✅ Nếu `DOMAIN=localhost` → Thêm port: `http://localhost:8124`
- ✅ Nếu `DOMAIN=https://yourdomain.com` → Không thêm port: `https://yourdomain.com`
- ✅ Nếu `DOMAIN=http://IP` → Thêm port: `http://IP:8124`

### **4. Ví Dụ Download URLs**

**Với localhost:**
```
http://localhost:8124/download/outputs/obj/2025-05-30/box_20250530104755.obj
http://localhost:8124/download/outputs/cad/2025-05-30/box_20250530104755.step
```

**Với production domain:**
```
https://api.yourdomain.com/download/outputs/obj/2025-05-30/box_20250530104755.obj
https://api.yourdomain.com/download/outputs/cad/2025-05-30/box_20250530104755.step
```

### **5. Test Download URLs**

**Sau khi thay đổi domain, test bằng:**
```bash
# Test PDF API
curl -X POST "http://localhost:8124/api-test/api/chat-pdf/" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf" \
  -F "user_input=200x200x2"

# Response sẽ có:
{
  "obj_export": "https://api.yourdomain.com/download/outputs/obj/...",
  "step_export": "https://api.yourdomain.com/download/outputs/cad/..."
}
```

### **6. Nginx Configuration (nếu cần)**

**Nếu sử dụng Nginx reverse proxy:**
```nginx
server {
    listen 80;
    server_name api.yourdomain.com;
    
    location / {
        proxy_pass http://localhost:8124;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /download/ {
        proxy_pass http://localhost:8124/download/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### **7. Restart Server**

**Sau khi thay đổi `.env`, restart server:**
```bash
cd /home/<USER>/DFM_engineer/tolery-api-ai
source ~/miniconda3/bin/activate
conda activate myenv
python run.py
```

**Kiểm tra log:**
```
2025-05-30 11:11:32,777 - tolery-main - INFO - Configured BASE_URL: https://your-production-domain.com
```

### **8. Các Files Đã Được Sửa**

- ✅ `.env` - Cấu hình DOMAIN
- ✅ `src/api/main.py` - BASE_URL configuration
- ✅ `src/crud/chat_processing.py` - _create_download_url function
- ✅ `src/crud/sessions.py` - Download URL generation (đã có sẵn)

---
**🎉 Bây giờ tất cả download URLs sẽ sử dụng domain bạn cấu hình thay vì localhost!**
