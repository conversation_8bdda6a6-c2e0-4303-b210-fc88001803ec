# Tolery CAD API - Final Configuration

## 🎯 **Overview**
Ứng dụng Tolery CAD API hiện tại có **2 API chính** được tích hợp hoàn hảo:

- **API-TEST**: `/api-test/` - 8 endpoints cho testing và development
- **API-PRODUCTION**: `/api-production/` - Production endpoints

## 🚀 **Quick Start**

### Khởi chạy ứng dụng
```bash
cd /home/<USER>/DFM_engineer/tolery-api-ai
source ~/miniconda3/bin/activate
conda activate myenv
python run.py
```

### Truy cập API Documentation
- **API-TEST**: http://localhost:8124/api-test/docs
- **API-PRODUCTION**: http://localhost:8124/api-production/docs
- **Main App**: http://localhost:8124/

## 📋 **API-TEST Endpoints (8 endpoints)**

### 1. **Session Management**
- `GET /api-test/api/sessions` - List all sessions
- `GET /api-test/api/sessions/{session_id}` - Get session info
- `DELETE /api-test/api/sessions/{session_id}` - Delete session

### 2. **Export Management**
- `GET /api-test/api/get-export` - Get export files (OBJ/STEP)

### 3. **Chat History**
- `GET /api-test/api/chat-history/{session_id}` - Get chat history

### 4. **Chat Processing**
- `POST /api-test/api/chat` - Regular text chat
- `POST /api-test/api/chat-pdf/` - PDF upload and processing
- `POST /api-test/api/chat-image/` - Image upload and processing

## 🏗️ **Architecture**

### **Main Application Structure**
```
src/api/
├── main.py                 # Main app (mounts sub-apps)
├── routes/                 # API-PRODUCTION routes (không sửa)
├── production/             # API-PRODUCTION app
│   └── main.py            # Production endpoints
└── test/                  # API-TEST app
    ├── main.py            # 8 core endpoints
    ├── pdf_chat.py        # PDF processing router
    ├── image_chat.py      # Image processing router
    ├── pdf_handler.py     # PDF handler (copied from src/utils)
    └── image_handler.py   # Image handler (copied from src/utils)
```

### **Key Features**
- ✅ **Clean separation**: API-TEST và API-PRODUCTION hoàn toàn độc lập
- ✅ **Local dependencies**: PDF/Image handlers được copy vào test directory
- ✅ **Complete functionality**: Tất cả 8 endpoints hoạt động đầy đủ
- ✅ **Database integration**: Kết nối MySQL thành công
- ✅ **CAD generation**: Text-to-CAD agent hoạt động

## 🧪 **Testing**

### Test API-TEST endpoints
```bash
# List sessions
curl -X GET "http://localhost:8124/api-test/api/sessions"

# Get session info
curl -X GET "http://localhost:8124/api-test/api/sessions/session_255feb_442763"

# Get exports
curl -X GET "http://localhost:8124/api-test/api/get-export?session_id=session_255feb_442763"

# Get chat history
curl -X GET "http://localhost:8124/api-test/api/chat-history/session_255feb_442763"

# Regular chat
curl -X POST "http://localhost:8124/api-test/api/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "Create a cube 10x10x10"}'
```

### Test PDF/Image APIs
```bash
# PDF processing
curl -X POST "http://localhost:8124/api-test/api/chat-pdf/" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf" \
  -F "user_input=Analyze this document"

# Image processing  
curl -X POST "http://localhost:8124/api-test/api/chat-image/" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@image.jpg" \
  -F "user_input=Analyze this image"
```

## 📊 **Status**

### ✅ **Hoàn thành**
- [x] 2 API chính: API-TEST và API-PRODUCTION
- [x] 8 endpoints trong API-TEST
- [x] PDF và Image processing
- [x] Database connectivity
- [x] CAD generation
- [x] Session management
- [x] Export file handling
- [x] Chat history
- [x] Local dependencies
- [x] Clean architecture

### 🎯 **URLs**
- **API-TEST Docs**: http://localhost:8124/api-test/docs
- **API-PRODUCTION Docs**: http://localhost:8124/api-production/docs
- **Health Check**: http://localhost:8124/api/health

## 🔧 **Configuration**
- **Port**: 8124
- **Database**: MySQL (**************:33061/atn_tolery)
- **Environment**: Conda myenv
- **Python**: 3.10.13

## 📝 **Notes**
- Folder `routes` không được sửa đổi (theo yêu cầu)
- PDF và Image handlers được copy từ `src/utils` vào `src/api/test/`
- Tất cả dependencies được import đúng cách
- Server khởi động thành công và tất cả endpoints hoạt động

---
**🎉 Cấu hình hoàn tất! Tất cả 8 endpoints API-TEST và API-PRODUCTION đều hoạt động hoàn hảo!**
