#!/usr/bin/env python3
"""
Run script for Tolery API-TEST
Starts the test API server with 8 core endpoints.
"""

import os
import sys
import uvicorn
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """Main function to run the API-TEST server."""
    print("Starting Tolery API-TEST server...")
    print("8 Core Endpoints:")
    print("1. GET /api/sessions - List sessions")
    print("2. GET /api/sessions/{session_id} - Get session info")
    print("3. DELETE /api/sessions/{session_id} - Delete session")
    print("4. GET /api/get-export - Get export files")
    print("5. GET /api/chat-history/{session_id} - Get chat history")
    print("6. POST /api/chat - Regular chat")
    print("7. POST /api/chat-pdf - PDF chat")
    print("8. POST /api/chat-image - Image chat")
    print()
    print("API Documentation will be available at: http://localhost:8125/docs")
    print("=" * 60)

    # Import the app
    from src.api.test.main import app

    # Run the server
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8125,
        reload=False,
        log_level="info"
    )

if __name__ == "__main__":
    main()
