# API-TEST vs API-PRODUCTION Comparison

## Overview
Dự án hiện có 2 hệ thống API riêng biệt:
- **API-TEST**: 8 endpoints cốt lõi, tập trung vào chức năng chính
- **API-PRODUCTION**: Hệ thống production đầy đủ với nhiều endpoints

## API-TEST (Port 8125)

### Location
- **Main file**: `src/api/test/main.py`
- **Run script**: `run_api_test.py`
- **Port**: 8125
- **Documentation**: http://localhost:8125/docs

### 8 Core Endpoints
1. `GET /api/sessions` - List sessions
2. `GET /api/sessions/{session_id}` - Get session info
3. `DELETE /api/sessions/{session_id}` - Delete session
4. `GET /api/get-export` - Get export files
5. `GET /api/chat-history/{session_id}` - Get chat history
6. `POST /api/chat` - Regular chat
7. `POST /api/chat-pdf` - PDF chat
8. `POST /api/chat-image` - Image chat

### Features
✅ **Simplified**: Chỉ các endpoints cần thiết
✅ **Focused**: Tập trung vào CAD generation
✅ **Clean**: Code sạch, dễ maintain
✅ **Tested**: Đã test và hoạt động tốt
✅ **PDF/Image**: Integrated PDF và Image processing
✅ **Session Management**: Auto-generate session_id
✅ **File Export**: OBJ và STEP files

## API-PRODUCTION (Port 8124)

### Location
- **Main file**: `src/api/production/main.py`
- **Port**: 8124
- **Documentation**: http://localhost:8124/docs

### Features
- Hệ thống production đầy đủ
- Nhiều endpoints hơn
- Có thể có các tính năng advanced khác

## Key Differences

| Aspect | API-TEST | API-PRODUCTION |
|--------|----------|----------------|
| **Purpose** | Testing & Core Functions | Production Ready |
| **Port** | 8125 | 8124 |
| **Endpoints** | 8 core endpoints | Full endpoint set |
| **Complexity** | Simple & Clean | More comprehensive |
| **PDF/Image** | ✅ Integrated | ❓ Status unknown |
| **Maintenance** | Easy to update | More complex |
| **Testing** | ✅ Fully tested | ❓ Status unknown |

## Usage Recommendations

### Use API-TEST when:
- ✅ Testing new features
- ✅ Development và debugging
- ✅ Cần chức năng cốt lõi
- ✅ Muốn response nhanh
- ✅ Testing PDF/Image processing

### Use API-PRODUCTION when:
- ✅ Production deployment
- ✅ Cần full feature set
- ✅ Production environment

## Development Workflow

### API-TEST Development
```bash
# Start API-TEST
cd /home/<USER>/DFM_engineer/tolery-api-ai
source ~/miniconda3/bin/activate
conda activate myenv
python run_api_test.py
```

### Testing Commands
```bash
# Test chat
curl -X POST "http://localhost:8125/api/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "Create a cube 10x10x10"}'

# Test sessions
curl -X GET "http://localhost:8125/api/sessions"

# Test exports
curl -X GET "http://localhost:8125/api/get-export?session_id=SESSION_ID"
```

## File Organization

### API-TEST Structure
```
src/api/test/
├── main.py              # 8 core endpoints
├── __init__.py
src/api/routes/
├── pdf_chat.py          # PDF processing
├── image_chat.py        # Image processing
run_api_test.py          # Run script
```

### API-PRODUCTION Structure
```
src/api/production/
├── main.py              # Production endpoints
├── ...                  # Other production files
```

## Current Status

### API-TEST ✅
- [x] All 8 endpoints implemented
- [x] Database connection working
- [x] Chat functionality tested
- [x] File export working (OBJ + STEP)
- [x] Session management working
- [x] PDF/Image routes created
- [x] Documentation complete

### API-PRODUCTION ❓
- Status needs verification
- May need updates to match API-TEST features

## Next Steps

1. **Continue using API-TEST** for development and testing
2. **Verify API-PRODUCTION** status and features
3. **Sync features** between TEST and PRODUCTION when ready
4. **Deploy API-PRODUCTION** when stable

## Conclusion

API-TEST hiện tại đã hoàn thiện và sẵn sàng sử dụng với 8 endpoints cốt lõi. Đây là hệ thống ideal cho development, testing, và các tính năng CAD generation cơ bản.
