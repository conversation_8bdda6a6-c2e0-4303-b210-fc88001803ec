# Tolery API-TEST Documentation

## Overview
API-TEST là hệ thống API test với 8 endpoints chính cho việc tạo và quản lý CAD models. API này được tích hợp vào ứng dụng chính trên port 8124 với prefix `/api-test/` và cung cấp đầy đủ chức năng cho việc chat, xử lý PDF/Image, và quản lý sessions.

## Quick Start

### Khởi chạy ứng dụng chính (bao gồm API-TEST)
```bash
cd /home/<USER>/DFM_engineer/tolery-api-ai
source ~/miniconda3/bin/activate
conda activate myenv
python run.py
```

### API Documentation
- **URL**: http://localhost:8124/api-test/docs
- **Port**: 8124 (ứng dụng chính)
- **Base URL**: http://localhost:8124/api-test/api

## 8 Core Endpoints

### 1. List Sessions
```bash
GET /api/sessions
```
**<PERSON><PERSON> tả**: <PERSON><PERSON><PERSON> danh sách tất cả sessions
**Response**: Array of session objects với session_id, created_at, updated_at

### 2. Get Session Info
```bash
GET /api/sessions/{session_id}
```
**Mô tả**: Lấy thông tin chi tiết của một session
**Response**: Session info với message_count và latest_code

### 3. Delete Session
```bash
DELETE /api/sessions/{session_id}
```
**Mô tả**: Xóa session và toàn bộ chat history
**Response**: Confirmation message

### 4. Get Export Files
```bash
GET /api/get-export?session_id={session_id}&types={obj,step}
```
**Mô tả**: Lấy danh sách file exports (OBJ/STEP) của session
**Parameters**:
- `session_id`: Required - Session ID
- `types`: Optional - Filter by file types (obj, step)

### 5. Get Chat History
```bash
GET /api/chat-history/{session_id}
```
**Mô tả**: Lấy lịch sử chat của session (hỗ trợ tất cả loại: chat, PDF, image)
**Response**: Messages array với timestamp, role, content, exports

### 6. Chat (Regular Text)
```bash
POST /api/chat
Content-Type: application/json

{
  "message": "Create a simple cube 10x10x10",
  "is_edit_request": false,
  "session_id": "optional_session_id"
}
```
**Mô tả**: Chat thông thường để tạo CAD models
**Response**: Chat response với obj_export và step_export URLs

### 7. Chat PDF
```bash
POST /api/chat-pdf
Content-Type: multipart/form-data

file: [PDF file]
session_id: "optional_session_id"
user_input: "additional instructions"
```
**Workflow**:
1. Upload PDF → Analyze to extract class/name
2. Pass analysis results to chat tool
3. Generate code/obj/step files

### 8. Chat Image
```bash
POST /api/chat-image
Content-Type: multipart/form-data

file: [Image file]
session_id: "optional_session_id"
user_input: "additional instructions"
```
**Workflow**:
1. Upload Image → Analyze to extract class/name
2. Pass analysis results to chat tool
3. Generate code/obj/step files

## Test Examples

### Test Chat API
```bash
curl -X POST "http://localhost:8124/api-test/api/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "Create a simple cube 10x10x10", "is_edit_request": false}'
```

### Test Get Export
```bash
curl -X GET "http://localhost:8124/api-test/api/get-export?session_id=session_255feb_442763"
```

### Test Session Info
```bash
curl -X GET "http://localhost:8124/api-test/api/sessions/session_255feb_442763"
```

## Key Features

### Session Management
- **Auto-generate session_id**: Nếu không cung cấp session_id, hệ thống tự tạo
- **Session continuity**: Sử dụng session_id để duy trì cuộc hội thoại
- **Latest code tracking**: Lưu trữ code mới nhất trong chat history

### File Export
- **Dual format**: Tự động tạo cả OBJ và STEP files
- **Organized storage**: Files được lưu theo ngày tháng
- **URL response**: Trả về đường dẫn file trong response

### Error Handling
- **Comprehensive logging**: Chi tiết log cho debugging
- **Error responses**: Trả về error message trong response
- **Database rollback**: Tự động rollback khi có lỗi

## Database Integration
- **MySQL**: Kết nối đến database development
- **Host**: **************:33061
- **Database**: atn_tolery
- **Tables**: sessions, chat_history

## File Structure
```
src/api/test/
├── main.py              # Main API-TEST application
├── __init__.py          # Package initialization
src/api/routes/
├── pdf_chat.py          # PDF chat router
├── image_chat.py        # Image chat router
run_api_test.py          # Run script for API-TEST
```

## Status
✅ **Hoạt động**: Tất cả 8 endpoints đã được test và hoạt động tốt
✅ **Database**: Kết nối thành công
✅ **File Export**: OBJ và STEP files được tạo thành công
✅ **Session Management**: Tạo và quản lý sessions hoạt động tốt
