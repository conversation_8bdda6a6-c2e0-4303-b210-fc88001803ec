# Database Management Guide

## 🗄️ **Database Cleanup Tools**

### **Available Scripts**

1. **`clear_database.py`** - X<PERSON>a tất cả dữ liệu, giữ lại cấu trúc
2. **`backup_database.py`** - Tạo backup trước khi xóa
3. **`reset_database.py`** - Backup + Clear (tổng hợp)

## 🧹 **Xóa Database (Đã Thực Hiện)**

### **Kết Quả Thành Công:**
```
✅ Database cleanup completed successfully!

📊 Verification - Table row counts:
   - chat_history: 0 rows (đã xóa 550 rows)
   - sessions: 0 rows (đã xóa 405 rows)  
   - shapes: 0 rows (đã trống)

💡 Database structure preserved!
```

### **Những Gì Đã Được Xóa:**
- ✅ **550 chat history entries** - Tất cả lịch sử chat
- ✅ **405 sessions** - Tất cả phiên làm việc
- ✅ **All file references** - Tất cả tham chiếu file
- ✅ **All user data** - Tất cả dữ liệu người dùng

### **Những Gì Được Giữ Lại:**
- ✅ **Database structure** - Cấu trúc database
- ✅ **Tables** - Các bảng (chat_history, sessions, shapes)
- ✅ **Columns** - Các cột và kiểu dữ liệu
- ✅ **Indexes** - Các chỉ mục
- ✅ **Constraints** - Các ràng buộc

## 🔄 **Sử Dụng Scripts**

### **1. Chỉ Xóa Dữ Liệu:**
```bash
cd /home/<USER>/DFM_engineer/tolery-api-ai
source ~/miniconda3/bin/activate
conda activate myenv
python clear_database.py
```

### **2. Backup Trước Khi Xóa:**
```bash
# Tạo backup
python backup_database.py

# Sau đó xóa
python clear_database.py
```

### **3. Backup + Clear Tự Động:**
```bash
python reset_database.py
```

## 💾 **Backup & Restore**

### **Tạo Backup:**
```bash
python backup_database.py
# Tạo file: backups/backup_atn_tolery_YYYYMMDD_HHMMSS.sql
```

### **Restore từ Backup:**
```bash
mysql -h ************** -P 33061 -u root -p atn_tolery < backups/backup_atn_tolery_20250530_111500.sql
```

## 📊 **Verification**

### **Kiểm Tra Database Trống:**
```bash
# API Test
curl -s http://localhost:8124/api-test/api/sessions
# Kết quả: []

# Database Query
mysql -h ************** -P 33061 -u root -p -e "SELECT COUNT(*) FROM atn_tolery.sessions;"
# Kết quả: 0
```

### **Kiểm Tra Cấu Trúc Còn Nguyên:**
```bash
mysql -h ************** -P 33061 -u root -p -e "SHOW TABLES FROM atn_tolery;"
# Kết quả: chat_history, sessions, shapes
```

## 🎯 **Khi Nào Sử Dụng**

### **Xóa Database Khi:**
- 🧪 **Testing**: Cần database sạch để test
- 🔄 **Reset**: Muốn bắt đầu lại từ đầu
- 🗑️ **Cleanup**: Xóa dữ liệu cũ không cần thiết
- 🚀 **Production**: Chuẩn bị cho production deployment

### **Backup Khi:**
- 💾 **Before major changes**: Trước khi thay đổi lớn
- 🔄 **Regular maintenance**: Bảo trì định kỳ
- 🚀 **Before deployment**: Trước khi deploy
- 🧪 **Before testing**: Trước khi test

## ⚠️ **Lưu Ý An Toàn**

### **Trước Khi Xóa:**
1. ✅ **Backup dữ liệu quan trọng**
2. ✅ **Xác nhận không cần dữ liệu cũ**
3. ✅ **Thông báo team members**
4. ✅ **Stop application nếu cần**

### **Sau Khi Xóa:**
1. ✅ **Verify database trống**
2. ✅ **Test API endpoints**
3. ✅ **Restart application**
4. ✅ **Monitor for errors**

## 🔧 **Troubleshooting**

### **Lỗi Thường Gặp:**

**1. Connection Error:**
```bash
# Kiểm tra .env file
cat .env | grep MYSQL

# Test connection
mysql -h ************** -P 33061 -u root -p
```

**2. Permission Error:**
```bash
# Kiểm tra quyền user
SHOW GRANTS FOR 'root'@'%';
```

**3. Foreign Key Error:**
```bash
# Script tự động disable/enable foreign keys
SET FOREIGN_KEY_CHECKS = 0;
# ... delete operations ...
SET FOREIGN_KEY_CHECKS = 1;
```

## 📁 **Files Created**

- ✅ `clear_database.py` - Main cleanup script
- ✅ `backup_database.py` - Backup utility
- ✅ `reset_database.py` - Combined backup + clear
- ✅ `backups/` - Backup storage directory

---

## 🎉 **Status: COMPLETED**

**Database đã được xóa sạch thành công!**
- ✅ 550 chat history entries removed
- ✅ 405 sessions removed  
- ✅ Database structure preserved
- ✅ Ready for fresh data

**API Verification:**
```bash
curl -s http://localhost:8124/api-test/api/sessions
# Result: [] (empty array)
```

**🚀 Database is now clean and ready for use!**
